<?php

if (@!include __DIR__ . '/../vendor/autoload.php') {
	die('Install dependencies using `composer install --dev`');
}

<PERSON>\Debugger::enable();

?>
<!DOCTYPE html><link rel="stylesheet" href="data/style.css">

<h1>Using Extension Methods | Dibi</h1>

<?php

$dibi = new Dibi\Connection([
	'driver' => 'sqlite3',
	'database' => 'data/sample.s3db',
]);


// using the "prototype" to add custom method to class Dibi\Result
Dibi\Result::extensionMethod('fetchShuffle', function (Dibi\Result $obj) {
	$all = $obj->fetchAll();
	shuffle($all);
	return $all;
});


// fetch complete result set shuffled
$res = $dibi->query('SELECT * FROM [customers]');
$all = $res->fetchShuffle();
<PERSON>\Dumper::dump($all);
