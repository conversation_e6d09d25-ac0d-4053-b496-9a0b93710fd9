<?php

/**
 * This file is part of the Dibi, smart database abstraction layer (https://dibiphp.com)
 * Copyright (c) 2005 <PERSON> (https://davidgrudl.com)
 */

namespace <PERSON><PERSON>\Reflection;

use <PERSON><PERSON>;


/**
 * Reflection metadata class for a foreign key.
 *
 * @property-read string $name
 * @property-read array $references
 */
class ForeignKey
{
	use Dibi\Strict;

	/** @var string */
	private $name;

	/** @var array of [local, foreign, onDelete, onUpdate] */
	private $references;


	public function __construct($name, array $references)
	{
		$this->name = $name;
		$this->references = $references;
	}


	/**
	 * @return string
	 */
	public function getName()
	{
		return $this->name;
	}


	/**
	 * @return array
	 */
	public function getReferences()
	{
		return $this->references;
	}
}
