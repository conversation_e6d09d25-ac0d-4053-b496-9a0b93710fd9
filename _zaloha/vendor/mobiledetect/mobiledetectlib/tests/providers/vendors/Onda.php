<?php
return array(
    'Onda' => array(
        'Mozilla/5.0 (Linux; Android 4.2.2; V975i Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.108 Safari/537.36' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Android; Tablet; rv:37.0) Gecko/37.0 Firefox/37.0' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Linux; Android 4.4.2; V975m Core4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Linux; U; Android 4.3; fr-fr; V975m Core4 Build/JSS15J) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Safari/537.16' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Linux; Android 4.3; V975m Core4 Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Safari/537.36' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Linux; Android 4.4.2; V812 Core4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36' => array('isMobile' => true, 'isTablet' => true),
        'Mozilla/5.0 (Linux; Android 7.0; V10 4G Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 YaBrowser/17.11.1.628.01 Safari/537.36' => array('isMobile' => true, 'isTablet' => true),
    )

);