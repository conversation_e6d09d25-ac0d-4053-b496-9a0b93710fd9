{"name": "nette/caching", "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "keywords": ["nette", "cache", "journal", "sqlite", "memcached"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": ">=5.6.0", "nette/finder": "^2.2 || ~3.0.0", "nette/utils": "^2.4 || ~3.0.0"}, "require-dev": {"nette/tester": "^2.0", "nette/di": "^2.4 || ~3.0.0", "latte/latte": "^2.4", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "conflict": {"nette/nette": "<2.2"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}