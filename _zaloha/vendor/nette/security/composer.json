{"name": "nette/security", "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "keywords": ["nette", "authentication", "authorization", "ACL"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": ">=5.6.0", "nette/utils": "~2.4"}, "require-dev": {"nette/di": "~2.4", "nette/http": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "conflict": {"nette/nette": "<2.2"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}