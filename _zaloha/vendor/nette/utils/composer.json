{"name": "nette/utils", "description": "🛠 Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "keywords": ["nette", "images", "json", "password", "validation", "utility", "string", "array", "core", "slugify", "utf-8", "unicode", "paginator", "datetime"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": ">=5.6.0"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image"}, "conflict": {"nette/nette": "<2.2"}, "autoload": {"classmap": ["src/"], "files": ["src/loader.php"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}