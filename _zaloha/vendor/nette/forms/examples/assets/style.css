/** common style for Nette examples */

html {
	font: 16px/1.5 sans-serif;
	border-top: 4.7em solid #F4EBDB;
}

body {
	max-width: 990px;
	margin: -4.7em auto 0;
	background: white;
	color: #333;
}

h1 {
	font-size: 1.9em;
	margin: .5em 0 1.5em;
	background: url(logo.png) right center no-repeat;
	color: #7A7772;
	text-shadow: 1px 1px 0 white;
}

fieldset {
	padding: .2em 1em 1em;
	margin: .5em 0;
	background: #E4F1FC;
	border: 1px solid #B2D1EB;
}

textarea,
select,
input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]):not([type="image"]):not([type="range"]) {
	padding: .3em .5em;
	color: black;
	background: white;
	border: 1px solid silver;
}

.has-error textarea,
.has-error select,
.has-error input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]):not([type="image"]):not([type="range"]) {
	border-color: #E22;
}

select {
	padding-right: .3em;
}

input[type="submit"] {
	font-size: 120%;
}

th {
	width: 10em;
	text-align: right;
	font-weight: normal;
}

.required label {
	font-weight: bold;
}

.error {
	color: #E22;
	font-weight: bold;
	margin-left: 1em;
}

footer a {
	font-size: 70%;
	color: gray;
}
