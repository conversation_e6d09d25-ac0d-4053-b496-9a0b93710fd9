Preprocessing
-------------
- tabs are converted to single space
- \r is removed


Comment
-------
Comment ::= '#' .*


Values
------
Value   ::= Boolean | Null | integer | float | String | DateTime | Literal | InlineArray | Entity
Boolean ::= 'true' | 'TRUE' | 'false' | 'FALSE' | 'yes' | 'YES' | 'no' | 'NO'
Null    ::= 'null' | 'NULL' | ''
String  ::= "word\u231" | 'word'
Literal ::= trimmed stream of characters [^#"',:=@[\]{}()\s!`] ( [^#,:=\]})(] | ':' [^\s,\]})] | \S '#' )*
Entity  ::= Value '(' ( ArrayEntry ',' )* ')'


InlineArray
-----------
InlineArray  ::= '{' ( ArrayEntry ',' )* '}' | '[' ( ArrayEntry ',' )* ']' | '(' ( ArrayEntry ',' )* ')'

ArrayEntry   ::= Value | KeyValuePair
KeyValuePair ::= Value '=' Value | Value ': ' Value


BlockArray
----------
BlockArray   ::= Indent ( '- ' Value | KeyValuePair ) EOL
